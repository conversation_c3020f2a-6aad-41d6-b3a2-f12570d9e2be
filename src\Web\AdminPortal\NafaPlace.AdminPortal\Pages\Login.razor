@page "/login"
@using NafaPlace.AdminPortal.Models.Auth
@using NafaPlace.AdminPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace Admin - Connexion</PageTitle>

<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="col-11 col-sm-8 col-md-6 col-lg-4 col-xl-3">
        <div class="card border-0 shadow-lg" style="border-radius: 16px; border: 2px solid rgba(255,255,255,0.3); backdrop-filter: blur(10px); max-width: 400px; margin: 0 auto;">
            <div class="card-body p-4" style="background: rgba(255,255,255,0.98); border-radius: 16px;"">
                <div class="text-center mb-3">
                    <div class="mb-2">
                        <i class="fas fa-shield-alt text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold text-dark mb-1">Admin Portal</h5>
                    <p class="text-muted small mb-0">Connexion sécurisée</p>
                </div>
                    
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @errorMessage
                        </div>
                    }
                    
                    <EditForm Model="@loginModel" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        
                        <div class="mb-3">
                            <label for="email" class="form-label fw-semibold text-dark mb-2">Email ou nom d'utilisateur</label>
                            <div class="input-group shadow-sm">
                                <span class="input-group-text bg-white border-2" style="border-color: #ced4da !important; border-right: none !important;">
                                    <i class="fas fa-envelope text-primary"></i>
                                </span>
                                <InputText id="email" @bind-Value="loginModel.Email" class="form-control border-2 border-start-0" placeholder="Entrez votre email ou nom d'utilisateur" style="border-color: #ced4da !important; padding: 12px 16px; font-size: 14px;" />
                            </div>
                            <ValidationMessage For="@(() => loginModel.Email)" class="text-danger small mt-1" />
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label fw-semibold text-dark mb-2">Mot de passe</label>
                            <div class="input-group shadow-sm">
                                <span class="input-group-text bg-white border-2" style="border-color: #ced4da !important; border-right: none !important;">
                                    <i class="fas fa-lock text-primary"></i>
                                </span>
                                <InputText id="password" type="password" @bind-Value="loginModel.Password" class="form-control border-2 border-start-0" placeholder="Entrez votre mot de passe" style="border-color: #ced4da !important; padding: 12px 16px; font-size: 14px;" />
                            </div>
                            <ValidationMessage For="@(() => loginModel.Password)" class="text-danger small mt-1" />
                        </div>

                        <div class="mb-3 form-check">
                            <InputCheckbox id="rememberMe" @bind-Value="loginModel.RememberMe" class="form-check-input" />
                            <label class="form-check-label small text-muted" for="rememberMe">Se souvenir de moi</label>
                        </div>
                        
                        <div class="d-grid gap-2 mb-3">
                            <button type="submit" class="btn btn-primary py-2" disabled="@isLoading" style="border-radius: 8px; font-weight: 500;">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-2">Connexion...</span>
                                }
                                else
                                {
                                    <span><i class="fas fa-sign-in-alt me-2"></i>Se connecter</span>
                                }
                            </button>
                        </div>
                    </EditForm>

                    <div class="text-center">
                        <a href="/" class="text-muted text-decoration-none small">
                            <i class="fas fa-arrow-left me-1"></i>Retour au site
                        </a>
                    </div>
                </div>
            </div>
    </div>
</div>

@code {
    private LoginModel loginModel = new LoginModel();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    
    protected override async Task OnInitializedAsync()
    {
        // Rediriger vers le dashboard si l'utilisateur est déjà connecté
        var currentUser = await AuthService.GetCurrentUserAsync();
        if (currentUser.IsAuthenticated)
        {
            NavigationManager.NavigateTo("/dashboard");
        }
    }
    
    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.LoginAsync(loginModel.Email, loginModel.Password);
            
            if (result.Success)
            {
                // Rediriger directement vers le dashboard après connexion réussie
                NavigationManager.NavigateTo("/dashboard", true);
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la connexion: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
    
    private class LoginModel
    {
        [Required(ErrorMessage = "L'email est requis")]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Le mot de passe est requis")]
        public string Password { get; set; } = string.Empty;
        
        public bool RememberMe { get; set; }
    }
}
