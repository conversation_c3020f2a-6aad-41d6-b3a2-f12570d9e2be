using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.Common.Models;
using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using BCrypt.Net;

namespace NafaPlace.Identity.Infrastructure.Services;

public class UserService : IUserService
{
    private readonly IdentityDbContext _dbContext;
    private readonly HttpClient _httpClient;

    public UserService(IdentityDbContext dbContext, HttpClient httpClient)
    {
        _dbContext = dbContext;
        _httpClient = httpClient;
    }

    public async Task<PagedResult<UserDto>> GetUsersAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null)
    {
        var query = _dbContext.Users.AsQueryable();

        // Appliquer le filtre de recherche si fourni
        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(u => u.Email.Contains(searchTerm) ||
                                   u.Username.Contains(searchTerm) ||
                                   u.FirstName.Contains(searchTerm) ||
                                   u.LastName.Contains(searchTerm));
        }

        // Calculer le nombre total d'éléments
        var totalCount = await query.CountAsync();

        // Appliquer la pagination
        var users = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        // Convertir en DTOs
        var userDtos = new List<UserDto>();
        foreach (var user in users)
        {
            // Récupérer les rôles de l'utilisateur
            var userRoles = await _dbContext.UserRoles
                .Where(ur => ur.UserId == user.Id)
                .Join(_dbContext.Roles,
                      ur => ur.RoleId,
                      r => r.Id,
                      (ur, r) => r.Name)
                .ToListAsync();

            userDtos.Add(new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                Username = user.Username,
                FirstName = user.FirstName,
                LastName = user.LastName,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt,
                Roles = userRoles
            });
        }

        return new PagedResult<UserDto>
        {
            Items = userDtos,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize
        };
    }

    public async Task<UserDto> GetUserProfileAsync(int userId)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        
        if (user == null)
        {
            throw new AuthenticationException("Utilisateur non trouvé");
        }

        // Récupérer les rôles de l'utilisateur
        var userRoles = await _dbContext.UserRoles
            .Where(ur => ur.UserId == userId)
            .Join(_dbContext.Roles,
                  ur => ur.RoleId,
                  r => r.Id,
                  (ur, r) => r.Name)
            .ToListAsync();

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            Username = user.Username,
            FirstName = user.FirstName,
            LastName = user.LastName,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            Roles = userRoles
        };
    }

    public async Task<UserDto> UpdateUserProfileAsync(int userId, UpdateUserProfileRequest request)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        
        if (user == null)
        {
            throw new AuthenticationException("Utilisateur non trouvé");
        }

        // Mettre à jour les informations du profil
        user.FirstName = request.FirstName;
        user.LastName = request.LastName;
        user.PhoneNumber = request.PhoneNumber ?? user.PhoneNumber;
        user.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        // Récupérer les rôles de l'utilisateur
        var userRoles = await _dbContext.UserRoles
            .Where(ur => ur.UserId == userId)
            .Join(_dbContext.Roles,
                  ur => ur.RoleId,
                  r => r.Id,
                  (ur, r) => r.Name)
            .ToListAsync();

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            Username = user.Username,
            FirstName = user.FirstName,
            LastName = user.LastName,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            Roles = userRoles
        };
    }

    public async Task<UserDto> UpdateUserAsync(int userId, UpdateUserRequest request)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            throw new AuthenticationException("Utilisateur non trouvé");
        }

        // Mettre à jour les informations de l'utilisateur
        user.Email = request.Email;
        user.FirstName = request.FirstName;
        user.LastName = request.LastName;
        user.PhoneNumber = request.PhoneNumber ?? user.PhoneNumber;
        user.IsActive = request.IsActive;
        user.UpdatedAt = DateTime.UtcNow;

        // Mettre à jour les rôles si fournis
        if (request.Roles.Any())
        {
            // Supprimer tous les rôles existants de l'utilisateur
            var existingUserRoles = await _dbContext.UserRoles
                .Where(ur => ur.UserId == userId)
                .ToListAsync();

            _dbContext.UserRoles.RemoveRange(existingUserRoles);

            // Ajouter les nouveaux rôles
            foreach (var roleName in request.Roles)
            {
                var role = await _dbContext.Roles.FirstOrDefaultAsync(r => r.Name == roleName);
                if (role != null)
                {
                    var userRole = new Domain.Models.UserRole
                    {
                        UserId = userId,
                        RoleId = role.Id,
                        CreatedAt = DateTime.UtcNow
                    };
                    _dbContext.UserRoles.Add(userRole);
                }
            }

            // Mettre à jour la propriété Roles de l'utilisateur (pour compatibilité)
            user.Roles = string.Join(",", request.Roles);
        }

        _dbContext.Users.Update(user);
        await _dbContext.SaveChangesAsync();

        // Récupérer les rôles de l'utilisateur mis à jour
        var userRoles = await _dbContext.UserRoles
            .Where(ur => ur.UserId == userId)
            .Join(_dbContext.Roles,
                  ur => ur.RoleId,
                  r => r.Id,
                  (ur, r) => r.Name)
            .ToListAsync();

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            Username = user.Username,
            FirstName = user.FirstName,
            LastName = user.LastName,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            Roles = userRoles
        };
    }

    public async Task UpdateUserRolesAsync(int userId, List<string> roles)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            throw new AuthenticationException("Utilisateur non trouvé");
        }

        // Supprimer tous les rôles existants de l'utilisateur
        var existingUserRoles = await _dbContext.UserRoles
            .Where(ur => ur.UserId == userId)
            .ToListAsync();

        _dbContext.UserRoles.RemoveRange(existingUserRoles);

        // Ajouter les nouveaux rôles
        foreach (var roleName in roles)
        {
            var role = await _dbContext.Roles.FirstOrDefaultAsync(r => r.Name == roleName);
            if (role != null)
            {
                var userRole = new Domain.Models.UserRole
                {
                    UserId = userId,
                    RoleId = role.Id,
                    CreatedAt = DateTime.UtcNow
                };
                _dbContext.UserRoles.Add(userRole);
            }
        }

        // Mettre à jour la propriété Roles de l'utilisateur (pour compatibilité)
        user.Roles = string.Join(",", roles);
        user.UpdatedAt = DateTime.UtcNow;

        _dbContext.Users.Update(user);
        await _dbContext.SaveChangesAsync();
    }

    public async Task ChangePasswordAsync(int userId, ChangePasswordRequest request)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        
        if (user == null)
        {
            throw new AuthenticationException("Utilisateur non trouvé");
        }

        // Vérifier le mot de passe actuel
        if (!VerifyPasswordHash(request.CurrentPassword, user.PasswordHash))
        {
            throw new AuthenticationException("Le mot de passe actuel est incorrect");
        }

        // Mettre à jour le mot de passe
        user.PasswordHash = HashPassword(request.NewPassword);
        user.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        // Envoyer un email de confirmation
        await SendPasswordChangedEmailAsync(user.Email, user.FirstName ?? user.Username);
    }

    private string HashPassword(string password)
    {
        // Utilisation de BCrypt pour le hachage du mot de passe
        return BCrypt.Net.BCrypt.HashPassword(password);
    }

    private bool VerifyPasswordHash(string password, string storedHash)
    {
        try
        {
            // Journalisation pour le débogage
            Console.WriteLine($"Vérification du mot de passe");
            Console.WriteLine($"Mot de passe stocké: {storedHash}");
            
            // Vérification avec BCrypt
            var isValid = BCrypt.Net.BCrypt.Verify(password, storedHash);
            Console.WriteLine($"Résultat de la vérification: {isValid}");
            
            return isValid;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la vérification du mot de passe: {ex.Message}");
            return false;
        }
    }

    private async Task SendPasswordChangedEmailAsync(string email, string userName)
    {
        try
        {
            var emailRequest = new
            {
                Email = email,
                UserName = userName
            };

            var json = JsonSerializer.Serialize(emailRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Appeler le service de notifications pour envoyer l'email
            var response = await _httpClient.PostAsync("http://notifications-api/api/notifications/email/password-changed", content);

            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Erreur lors de l'envoi de l'email de confirmation: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'envoi de l'email de confirmation: {ex.Message}");
            // Ne pas faire échouer le changement de mot de passe si l'email ne peut pas être envoyé
        }
    }

    public async Task InitiatePasswordResetAsync(string email)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Email == email);

        if (user == null)
        {
            // Ne pas révéler si l'utilisateur existe ou non
            return;
        }

        // Générer un token de réinitialisation
        var resetToken = GenerateResetToken();
        var resetTokenExpiry = DateTime.UtcNow.AddHours(24); // Token valide 24h

        // Stocker le token dans la base de données (vous pourriez créer une table séparée)
        user.ResetToken = resetToken;
        user.ResetTokenExpiry = resetTokenExpiry;
        user.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        // Créer le lien de réinitialisation
        var resetLink = $"http://localhost:8080/auth/reset-password?token={resetToken}&email={Uri.EscapeDataString(email)}";

        // Envoyer l'email de réinitialisation
        await SendPasswordResetEmailAsync(email, user.FirstName ?? user.Username, resetLink);
    }

    public async Task ResetPasswordAsync(ResetPasswordRequest request)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u =>
            u.Email == request.Email &&
            u.ResetToken == request.Token &&
            u.ResetTokenExpiry > DateTime.UtcNow);

        if (user == null)
        {
            throw new AuthenticationException("Token de réinitialisation invalide ou expiré");
        }

        // Réinitialiser le mot de passe
        user.PasswordHash = HashPassword(request.NewPassword);
        user.ResetToken = null;
        user.ResetTokenExpiry = null;
        user.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        // Envoyer un email de confirmation
        await SendPasswordChangedEmailAsync(user.Email, user.FirstName ?? user.Username);
    }

    private string GenerateResetToken()
    {
        // Générer un token sécurisé de 32 caractères
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    private async Task SendPasswordResetEmailAsync(string email, string userName, string resetLink)
    {
        try
        {
            var emailRequest = new
            {
                Email = email,
                UserName = userName,
                ResetLink = resetLink
            };

            var json = JsonSerializer.Serialize(emailRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Appeler le service de notifications pour envoyer l'email
            var response = await _httpClient.PostAsync("http://notifications-api/api/notifications/email/password-reset", content);

            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Erreur lors de l'envoi de l'email de réinitialisation: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'envoi de l'email de réinitialisation: {ex.Message}");
        }
    }
}
