@page "/login"
@using NafaPlace.SellerPortal.Models.Auth
@using NafaPlace.SellerPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace - Connexion Vendeur</PageTitle>

<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100" style="background-color: #f8f9fa;">
    <div class="col-md-4 col-lg-3">
        <div class="card border-0 shadow-sm" style="border-radius: 12px;">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="fas fa-store text-primary" style="font-size: 2.5rem;"></i>
                    </div>
                    <h4 class="fw-bold text-dark mb-1">Seller Portal</h4>
                    <p class="text-muted small mb-0">Espace vendeur</p>
                </div>
                    
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @errorMessage
                        </div>
                    }
                    
                    <EditForm Model="@loginRequest" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-envelope text-muted"></i>
                                </span>
                                <InputText id="email" @bind-Value="loginRequest.Email" class="form-control border-start-0 ps-0" placeholder="Email" />
                            </div>
                            <ValidationMessage For="@(() => loginRequest.Email)" class="text-danger small" />
                        </div>

                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-lock text-muted"></i>
                                </span>
                                <InputText id="password" type="password" @bind-Value="loginRequest.Password" class="form-control border-start-0 ps-0" placeholder="Mot de passe" />
                            </div>
                            <ValidationMessage For="@(() => loginRequest.Password)" class="text-danger small" />
                        </div>
                        
                        <div class="mb-3 form-check">
                            <InputCheckbox id="rememberMe" @bind-Value="loginRequest.RememberMe" class="form-check-input" />
                            <label class="form-check-label small text-muted" for="rememberMe">Se souvenir de moi</label>
                        </div>

                        <div class="d-grid gap-2 mb-3">
                            <button type="submit" class="btn btn-primary py-2" disabled="@isLoading" style="border-radius: 8px; font-weight: 500;">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-2">Connexion...</span>
                                }
                                else
                                {
                                    <span><i class="fas fa-sign-in-alt me-2"></i>Se connecter</span>
                                }
                            </button>
                        </div>
                    </EditForm>

                    <div class="text-center mb-3">
                        <a href="/forgot-password" class="text-primary text-decoration-none small">
                            <i class="fas fa-key me-1"></i>Mot de passe oublié ?
                        </a>
                    </div>

                    <div class="text-center mb-3">
                        <p class="small text-muted mb-2">Vous n'avez pas de compte ?</p>
                        <a href="/register" class="text-primary text-decoration-none small fw-bold">
                            <i class="fas fa-user-plus me-1"></i>Inscrivez-vous
                        </a>
                    </div>

                    <div class="text-center">
                        <a href="/" class="text-muted text-decoration-none small">
                            <i class="fas fa-arrow-left me-1"></i>Retour au site
                        </a>
                    </div>
                </div>
            </div>
    </div>
</div>

@code {
    private LoginRequest loginRequest = new LoginRequest();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    
    protected override async Task OnInitializedAsync()
    {
        // Rediriger vers le tableau de bord si l'utilisateur est déjà connecté
        var currentUser = await AuthService.GetCurrentUserAsync();
        if (currentUser.IsAuthenticated && currentUser.Roles.Contains("Seller"))
        {
            NavigationManager.NavigateTo("/dashboard");
        }
    }
    
    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.LoginAsync(loginRequest.Email, loginRequest.Password);
            
            if (result.Success)
            {
                // Vérifier si l'utilisateur a le rôle "Seller"
                var currentUser = await AuthService.GetCurrentUserAsync();
                if (currentUser.Roles.Contains("Seller"))
                {
                    // Rediriger vers le tableau de bord du vendeur
                    NavigationManager.NavigateTo("/dashboard", true);
                }
                else
                {
                    // L'utilisateur n'a pas le rôle "Seller"
                    await AuthService.LogoutAsync();
                    errorMessage = "Vous n'avez pas les autorisations nécessaires pour accéder à l'espace vendeur.";
                }
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la connexion: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
