@page "/login"
@using NafaPlace.SellerPortal.Models.Auth
@using NafaPlace.SellerPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace - Connexion Vendeur</PageTitle>

<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="col-11 col-sm-8 col-md-6 col-lg-4 col-xl-3">
        <div class="card border-0 shadow-lg" style="border-radius: 16px; border: 2px solid rgba(255,255,255,0.3); backdrop-filter: blur(10px); max-width: 400px; margin: 0 auto;">
            <div class="card-body p-4" style="background: rgba(255,255,255,0.98); border-radius: 16px;"">
                <div class="text-center mb-3">
                    <div class="mb-2">
                        <i class="fas fa-store text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold text-dark mb-1">Seller Portal</h5>
                    <p class="text-muted small mb-0">Espace vendeur</p>
                </div>
                    
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @errorMessage
                        </div>
                    }
                    
                    <EditForm Model="@loginRequest" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        
                        <div class="mb-2">
                            <div class="input-group shadow-sm">
                                <span class="input-group-text bg-white border-2" style="border-color: #ced4da !important; border-right: none !important;">
                                    <i class="fas fa-envelope text-primary"></i>
                                </span>
                                <InputText id="email" @bind-Value="loginRequest.Email" class="form-control border-2 border-start-0" placeholder="Email ou nom d'utilisateur" style="border-color: #ced4da !important; padding: 10px 14px; font-size: 14px;" />
                            </div>
                            <ValidationMessage For="@(() => loginRequest.Email)" class="text-danger small mt-1" />
                        </div>

                        <div class="mb-2">
                            <div class="input-group shadow-sm">
                                <span class="input-group-text bg-white border-2" style="border-color: #ced4da !important; border-right: none !important;">
                                    <i class="fas fa-lock text-primary"></i>
                                </span>
                                <InputText id="password" type="password" @bind-Value="loginRequest.Password" class="form-control border-2 border-start-0" placeholder="Mot de passe" style="border-color: #ced4da !important; padding: 10px 14px; font-size: 14px;" />
                            </div>
                            <ValidationMessage For="@(() => loginRequest.Password)" class="text-danger small mt-1" />
                        </div>
                        
                        <div class="mb-2 form-check">
                            <InputCheckbox id="rememberMe" @bind-Value="loginRequest.RememberMe" class="form-check-input" />
                            <label class="form-check-label small text-dark" for="rememberMe">Se souvenir de moi</label>
                        </div>

                        <div class="d-grid gap-2 mb-2">
                            <button type="submit" class="btn btn-primary py-2" disabled="@isLoading" style="border-radius: 8px; font-weight: 500;">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-2">Connexion...</span>
                                }
                                else
                                {
                                    <span><i class="fas fa-sign-in-alt me-2"></i>Se connecter</span>
                                }
                            </button>
                        </div>
                    </EditForm>

                    <div class="text-center mb-2">
                        <a href="/forgot-password" class="text-primary text-decoration-none small">
                            <i class="fas fa-key me-1"></i>Mot de passe oublié ?
                        </a>
                    </div>

                    <div class="text-center mb-2">
                        <p class="small text-muted mb-1">Pas de compte ?</p>
                        <a href="/register" class="text-primary text-decoration-none small fw-bold">
                            <i class="fas fa-user-plus me-1"></i>Inscrivez-vous
                        </a>
                    </div>

                    <div class="text-center">
                        <a href="/" class="text-muted text-decoration-none small">
                            <i class="fas fa-arrow-left me-1"></i>Retour au site
                        </a>
                    </div>
                </div>
            </div>
    </div>
</div>

@code {
    private LoginRequest loginRequest = new LoginRequest();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    
    protected override async Task OnInitializedAsync()
    {
        // Rediriger vers le tableau de bord si l'utilisateur est déjà connecté
        var currentUser = await AuthService.GetCurrentUserAsync();
        if (currentUser.IsAuthenticated && currentUser.Roles.Contains("Seller"))
        {
            NavigationManager.NavigateTo("/dashboard");
        }
    }
    
    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.LoginAsync(loginRequest.Email, loginRequest.Password);
            
            if (result.Success)
            {
                // Vérifier si l'utilisateur a le rôle "Seller"
                var currentUser = await AuthService.GetCurrentUserAsync();
                if (currentUser.Roles.Contains("Seller"))
                {
                    // Rediriger vers le tableau de bord du vendeur
                    NavigationManager.NavigateTo("/dashboard", true);
                }
                else
                {
                    // L'utilisateur n'a pas le rôle "Seller"
                    await AuthService.LogoutAsync();
                    errorMessage = "Vous n'avez pas les autorisations nécessaires pour accéder à l'espace vendeur.";
                }
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la connexion: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
