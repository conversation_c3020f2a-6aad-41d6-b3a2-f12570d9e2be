{"ConnectionStrings": {"DefaultConnection": "Host=localhost:5436;Database=NafaPlace.Notifications;Username=postgres;Password=*****************"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "NafaPlace", "Audience": "NafaPlace-Users", "ExpirationInMinutes": 60}, "Email": {"SmtpHost": "smtp.gmail.com", "SmtpPort": "587", "SmtpUsername": "<EMAIL>", "SmtpPassword": "hpgn kyce acgj abkk", "EnableSsl": "true", "FromEmail": "<EMAIL>", "FromName": "NafaPlace"}, "Environment": "Development", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}