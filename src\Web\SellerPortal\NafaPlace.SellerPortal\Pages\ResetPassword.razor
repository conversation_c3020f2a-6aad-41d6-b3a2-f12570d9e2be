@page "/reset-password"
@using NafaPlace.SellerPortal.Models
@using NafaPlace.SellerPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Réinitialiser le mot de passe - Seller Portal</PageTitle>

<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100" style="background-color: #f8f9fa;">
    <div class="col-md-4 col-lg-3">
        <div class="card border-0 shadow-sm" style="border-radius: 12px;">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="fas fa-lock text-primary" style="font-size: 2.5rem;"></i>
                    </div>
                    <h4 class="fw-bold text-dark mb-1">Nouveau mot de passe</h4>
                    <p class="text-muted small mb-0">Choisissez un nouveau mot de passe sécurisé</p>
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>@errorMessage
                    </div>
                }

                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>@successMessage
                    </div>
                }

                @if (!passwordReset)
                {
                    <EditForm Model="@resetPasswordRequest" OnValidSubmit="HandleResetPasswordSubmit">
                        <DataAnnotationsValidator />

                        <div class="mb-3">
                            <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <InputText type="password" class="form-control" id="newPassword" placeholder="Nouveau mot de passe" @bind-Value="resetPasswordRequest.NewPassword" />
                            </div>
                            <ValidationMessage For="@(() => resetPasswordRequest.NewPassword)" />
                        </div>

                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <InputText type="password" class="form-control" id="confirmPassword" placeholder="Confirmer le mot de passe" @bind-Value="resetPasswordRequest.ConfirmPassword" />
                            </div>
                            <ValidationMessage For="@(() => resetPasswordRequest.ConfirmPassword)" />
                        </div>

                        <div class="d-grid gap-2 mb-3">
                            <button type="submit" class="btn btn-primary py-2" disabled="@isLoading" style="border-radius: 8px; font-weight: 500;">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-2">Réinitialisation...</span>
                                }
                                else
                                {
                                    <span><i class="fas fa-save me-2"></i>Réinitialiser</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                }

                <div class="text-center">
                    <a href="/login" class="text-primary text-decoration-none small fw-bold">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la connexion
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private ResetPasswordRequest resetPasswordRequest = new();
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;
    private bool passwordReset = false;

    protected override async Task OnInitializedAsync()
    {
        // Récupérer les paramètres de l'URL
        var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
        var query = uri.Query;

        // Parser manuellement les paramètres de requête
        if (!string.IsNullOrEmpty(query) && query.StartsWith("?"))
        {
            var queryString = query.Substring(1);
            var pairs = queryString.Split('&');

            foreach (var pair in pairs)
            {
                var keyValue = pair.Split('=');
                if (keyValue.Length == 2)
                {
                    var key = Uri.UnescapeDataString(keyValue[0]);
                    var value = Uri.UnescapeDataString(keyValue[1]);

                    if (key == "token")
                        resetPasswordRequest.Token = value;
                    else if (key == "email")
                        resetPasswordRequest.Email = value;
                }
            }
        }


        if (string.IsNullOrEmpty(resetPasswordRequest.Token) || string.IsNullOrEmpty(resetPasswordRequest.Email))
        {
            errorMessage = "Lien de réinitialisation invalide. Veuillez demander un nouveau lien.";
        }
    }

    private async Task HandleResetPasswordSubmit()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            if (resetPasswordRequest.NewPassword != resetPasswordRequest.ConfirmPassword)
            {
                errorMessage = "Les mots de passe ne correspondent pas.";
                return;
            }

            // Appeler l'API pour réinitialiser le mot de passe
            var response = await AuthService.ResetPasswordAsync(resetPasswordRequest);

            if (response.Success)
            {
                passwordReset = true;
                successMessage = "Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter.";
                
                // Rediriger vers la page de connexion après 3 secondes
                await Task.Delay(3000);
                NavigationManager.NavigateTo("/login");
            }
            else
            {
                errorMessage = response.Message ?? "Une erreur s'est produite. Veuillez réessayer.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
