using System.Net.Http.Json;
using System.Net.Http.Headers;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using NafaPlace.SellerPortal.Models.Auth;
using Microsoft.Extensions.Configuration;

namespace NafaPlace.SellerPortal.Services;

public class AuthService : IAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly AuthenticationStateProvider _authStateProvider;

    // Event to notify authentication state changes
    public event Action? AuthenticationStateChanged;

    public AuthService(IHttpClientFactory httpClientFactory, ILocalStorageService localStorage, 
        AuthenticationStateProvider authStateProvider)
    {
        _httpClient = httpClientFactory.CreateClient("IdentityApi");
        _localStorage = localStorage;
        _authStateProvider = authStateProvider;
    }

    public async Task<AuthResponse> LoginAsync(string email, string password)
    {
        try
        {
            Console.WriteLine($"Tentative de connexion avec l'utilisateur: {email}");
            
            // Créer une requête adaptée au format attendu par l'API
            var apiRequest = new
            {
                Email = email,
                Username = string.Empty, // Laisser vide pour utiliser l'email
                Password = password
            };
            
            // Envoyer la requête au format attendu par l'API
            var response = await _httpClient.PostAsJsonAsync("api/auth/login", apiRequest);

            Console.WriteLine($"Réponse du serveur: {response.StatusCode}");
            
            if (response.IsSuccessStatusCode)
            {
                var authResponse = await response.Content.ReadFromJsonAsync<AuthResponse>();
                if (authResponse != null)
                {
                    Console.WriteLine("Connexion réussie, récupération du token");

                    // Déterminer quel token utiliser (AccessToken ou Token)
                    string token = authResponse.AccessToken ?? string.Empty;

                    Console.WriteLine($"Token récupéré: {(string.IsNullOrEmpty(token) ? "Non" : "Oui")}");

                    if (!string.IsNullOrEmpty(token))
                    {
                        // Supprimer les guillemets éventuels autour du token avant de le stocker
                        token = token.Trim('"');

                        // Stocker le token dans le localStorage
                        await _localStorage.SetItemAsync("authToken", token);
                        await _localStorage.SetItemAsync("refreshToken", authResponse.RefreshToken);

                        // Mettre à jour l'état d'authentification
                        ((ApiAuthenticationStateProvider)_authStateProvider).MarkUserAsAuthenticated(token);

                        // Configurer l'en-tête d'autorisation pour les futures requêtes
                        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

                        // Notifier les composants du changement d'état d'authentification
                        AuthenticationStateChanged?.Invoke();

                        // S'assurer que le Token est défini pour la vérification
                        authResponse.Success = true;

                        Console.WriteLine("État d'authentification mis à jour");
                        return new AuthResponse
                        {
                            Success = true,
                            Message = "Connexion réussie",
                            AccessToken = token,
                            RefreshToken = authResponse.RefreshToken,
                            User = authResponse.User
                        };
                    }
                    else
                    {
                        Console.WriteLine("Erreur: Token vide reçu de l'API");
                    }
                }
            }
            
            var errorResponse = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Erreur de connexion: {errorResponse}");
            
            return new AuthResponse 
            { 
                Success = false, 
                Message = errorResponse ?? "Échec de la connexion. Vérifiez vos identifiants."
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la connexion: {ex.Message}");
            return new AuthResponse 
            { 
                Success = false, 
                Message = $"Une erreur est survenue: {ex.Message}" 
            };
        }
    }
    

    public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
    {
        try
        {
            Console.WriteLine($"Tentative d'inscription pour: {request.Email}");

            // Envoyer la requête à l'endpoint d'inscription des vendeurs
            var response = await _httpClient.PostAsJsonAsync("api/auth/register-seller", request);

            Console.WriteLine($"Réponse du serveur: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var authResponse = await response.Content.ReadFromJsonAsync<AuthResponse>();
                if (authResponse != null)
                {
                    Console.WriteLine("Inscription réussie, récupération du token");

                    // Stocker le token dans le localStorage
                    await _localStorage.SetItemAsync("authToken", authResponse.AccessToken);
                    await _localStorage.SetItemAsync("refreshToken", authResponse.RefreshToken);

                    // Mettre à jour l'état d'authentification
                    ((ApiAuthenticationStateProvider)_authStateProvider).MarkUserAsAuthenticated(authResponse.AccessToken);

                    // Configurer l'en-tête d'autorisation pour les futures requêtes
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResponse.AccessToken);

                    // Notifier les composants du changement d'état d'authentification
                    AuthenticationStateChanged?.Invoke();

                    return new AuthResponse
                    {
                        Success = true,
                        Message = "Inscription réussie",
                        AccessToken = authResponse.AccessToken,
                        RefreshToken = authResponse.RefreshToken,
                        User = authResponse.User
                    };
                }
            }

            var errorResponse = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Erreur d'inscription: {errorResponse}");

            return new AuthResponse
            {
                Success = false,
                Message = errorResponse ?? "Échec de l'inscription. Veuillez réessayer."
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de l'inscription: {ex.Message}");
            return new AuthResponse
            {
                Success = false,
                Message = $"Une erreur est survenue: {ex.Message}"
            };
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            // Supprimer les tokens du localStorage
            await _localStorage.RemoveItemAsync("authToken");
            await _localStorage.RemoveItemAsync("refreshToken");
            
            // Mettre à jour l'état d'authentification
            ((ApiAuthenticationStateProvider)_authStateProvider).MarkUserAsLoggedOut();
            
            // Supprimer l'en-tête d'autorisation pour les futures requêtes
            _httpClient.DefaultRequestHeaders.Authorization = null;
            
            // Notifier les composants du changement d'état d'authentification
            AuthenticationStateChanged?.Invoke();
            
            Console.WriteLine("Déconnexion réussie");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la déconnexion: {ex.Message}");
        }
    }

    public async Task<UserDto> GetCurrentUserAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            
            if (string.IsNullOrEmpty(token))
            {
                return new UserDto { IsAuthenticated = false };
            }
            
            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            
            // Récupérer les informations de l'utilisateur courant
            var response = await _httpClient.GetAsync("api/auth/current-user");
            
            if (response.IsSuccessStatusCode)
            {
                var user = await response.Content.ReadFromJsonAsync<UserDto>();
                if (user != null)
                {
                    user.IsAuthenticated = true;
                    return user;
                }
            }
            
            return new UserDto { IsAuthenticated = false };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la récupération de l'utilisateur courant: {ex.Message}");
            return new UserDto { IsAuthenticated = false };
        }
    }

    public async Task<AuthResponse> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        try
        {
            Console.WriteLine($"Demande de réinitialisation pour {request.Email}");

            var response = await _httpClient.PostAsJsonAsync("api/users/forgot-password", request);

            Console.WriteLine($"Réponse du serveur: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                return new AuthResponse
                {
                    Success = true,
                    Message = "Email de réinitialisation envoyé avec succès"
                };
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Erreur de la réponse: {errorContent}");
                return new AuthResponse
                {
                    Success = false,
                    Message = "Une erreur s'est produite lors de l'envoi de l'email"
                };
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la demande de réinitialisation: {ex.Message}");
            return new AuthResponse
            {
                Success = false,
                Message = $"Une erreur est survenue: {ex.Message}"
            };
        }
    }

    public async Task<AuthResponse> ResetPasswordAsync(ResetPasswordRequest request)
    {
        try
        {
            Console.WriteLine($"Réinitialisation du mot de passe pour {request.Email}");

            var response = await _httpClient.PostAsJsonAsync("api/users/reset-password", request);

            Console.WriteLine($"Réponse du serveur: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                return new AuthResponse
                {
                    Success = true,
                    Message = "Mot de passe réinitialisé avec succès"
                };
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Erreur de la réponse: {errorContent}");
                return new AuthResponse
                {
                    Success = false,
                    Message = "Token invalide ou expiré"
                };
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la réinitialisation: {ex.Message}");
            return new AuthResponse
            {
                Success = false,
                Message = $"Une erreur est survenue: {ex.Message}"
            };
        }
    }
}
