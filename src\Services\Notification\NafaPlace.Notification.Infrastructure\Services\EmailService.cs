using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Notification.Application.DTOs;
using NafaPlace.Notification.Application.Services;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace NafaPlace.Notification.Infrastructure.Services;

public class EmailService : IEmailService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EmailService> _logger;
    private readonly SmtpClient _smtpClient;
    private readonly string _fromEmail;
    private readonly string _fromName;

    public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _fromEmail = _configuration["Email:FromEmail"] ?? "<EMAIL>";
        _fromName = _configuration["Email:FromName"] ?? "NafaPlace";

        // Configuration SMTP
        _smtpClient = new SmtpClient
        {
            Host = _configuration["Email:SmtpHost"] ?? "smtp.gmail.com",
            Port = int.Parse(_configuration["Email:SmtpPort"] ?? "587"),
            EnableSsl = bool.Parse(_configuration["Email:EnableSsl"] ?? "true"),
            Credentials = new NetworkCredential(
                _configuration["Email:Username"] ?? "",
                _configuration["Email:Password"] ?? ""
            )
        };
    }

    public async Task<bool> SendAsync(EmailNotificationDto email)
    {
        try
        {
            _logger.LogInformation("Envoi d'email à {To}: {Subject}", email.To, email.Subject);

            // Vérifier si l'email est supprimé
            if (await IsEmailSuppressedAsync(email.To))
            {
                _logger.LogWarning("Email supprimé, envoi annulé: {Email}", email.To);
                return false;
            }

            var mailMessage = new MailMessage
            {
                From = new MailAddress(_fromEmail, _fromName),
                Subject = email.Subject,
                Body = email.HtmlBody,
                IsBodyHtml = true
            };

            mailMessage.To.Add(email.To);

            // Ajouter CC et BCC si spécifiés
            if (email.Cc != null)
            {
                foreach (var cc in email.Cc)
                {
                    mailMessage.CC.Add(cc);
                }
            }

            if (email.Bcc != null)
            {
                foreach (var bcc in email.Bcc)
                {
                    mailMessage.Bcc.Add(bcc);
                }
            }

            // Ajouter le contenu texte si disponible
            if (!string.IsNullOrEmpty(email.TextBody))
            {
                var textView = AlternateView.CreateAlternateViewFromString(email.TextBody, null, "text/plain");
                mailMessage.AlternateViews.Add(textView);
            }

            // Ajouter les pièces jointes
            if (email.Attachments != null)
            {
                foreach (var attachment in email.Attachments)
                {
                    var stream = new MemoryStream(attachment.Content);
                    var mailAttachment = new Attachment(stream, attachment.FileName, attachment.ContentType);
                    mailMessage.Attachments.Add(mailAttachment);
                }
            }

            // Ajouter les headers personnalisés
            foreach (var header in email.Headers)
            {
                mailMessage.Headers.Add(header.Key, header.Value);
            }

            // Ajouter le tracking ID
            var trackingId = Guid.NewGuid().ToString();
            mailMessage.Headers.Add("X-Tracking-ID", trackingId);

            await _smtpClient.SendMailAsync(mailMessage);

            _logger.LogInformation("Email envoyé avec succès à {To} - Tracking ID: {TrackingId}", email.To, trackingId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi d'email à {To}", email.To);
            return false;
        }
    }

    public async Task<int> SendBulkAsync(List<EmailNotificationDto> emails)
    {
        var successCount = 0;

        foreach (var email in emails)
        {
            try
            {
                if (await SendAsync(email))
                {
                    successCount++;
                }
                
                // Délai entre les envois pour éviter le spam
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'envoi d'email en lot à {To}", email.To);
            }
        }

        _logger.LogInformation("Envoi en lot terminé: {Success}/{Total} emails envoyés", successCount, emails.Count);
        return successCount;
    }

    public async Task<bool> SendTemplatedAsync(string to, string templateId, Dictionary<string, object> templateData)
    {
        try
        {
            var template = await GetTemplateAsync(templateId);
            if (template == null)
            {
                _logger.LogError("Template non trouvé: {TemplateId}", templateId);
                return false;
            }

            var renderedContent = await RenderTemplateAsync(templateId, templateData);
            var renderedSubject = await RenderStringTemplateAsync(template.Subject, templateData);

            var email = new EmailNotificationDto
            {
                To = to,
                Subject = renderedSubject,
                HtmlBody = renderedContent,
                TextBody = template.TextContent,
                TemplateId = templateId
            };

            return await SendAsync(email);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi d'email avec template {TemplateId} à {To}", templateId, to);
            return false;
        }
    }

    public async Task<int> SendBulkTemplatedAsync(List<string> recipients, string templateId, Dictionary<string, object> templateData)
    {
        var successCount = 0;

        foreach (var recipient in recipients)
        {
            try
            {
                if (await SendTemplatedAsync(recipient, templateId, templateData))
                {
                    successCount++;
                }
                
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'envoi d'email template en lot à {To}", recipient);
            }
        }

        return successCount;
    }

    // Emails spécialisés
    public async Task SendWelcomeEmailAsync(string email, string userName, string activationLink)
    {
        var templateData = new Dictionary<string, object>
        {
            ["userName"] = userName,
            ["activationLink"] = activationLink,
            ["supportEmail"] = "<EMAIL>"
        };

        await SendTemplatedAsync(email, "welcome", templateData);
    }

    public async Task SendOrderConfirmationAsync(string email, int orderId, decimal amount, List<OrderItemDto> items)
    {
        var templateData = new Dictionary<string, object>
        {
            ["orderId"] = orderId,
            ["amount"] = amount,
            ["currency"] = "GNF",
            ["items"] = items,
            ["orderDate"] = DateTime.UtcNow.ToString("dd/MM/yyyy"),
            ["trackingUrl"] = $"https://nafaplace.com/orders/{orderId}"
        };

        await SendTemplatedAsync(email, "order_confirmation", templateData);
    }

    public async Task SendOrderStatusUpdateAsync(string email, int orderId, string status, string? trackingNumber = null)
    {
        var templateData = new Dictionary<string, object>
        {
            ["orderId"] = orderId,
            ["status"] = status,
            ["statusDate"] = DateTime.UtcNow.ToString("dd/MM/yyyy HH:mm"),
            ["trackingUrl"] = $"https://nafaplace.com/orders/{orderId}"
        };

        if (!string.IsNullOrEmpty(trackingNumber))
        {
            templateData["trackingNumber"] = trackingNumber;
        }

        await SendTemplatedAsync(email, "order_status_update", templateData);
    }

    public async Task SendPaymentConfirmationAsync(string email, int orderId, decimal amount, string paymentMethod)
    {
        var templateData = new Dictionary<string, object>
        {
            ["orderId"] = orderId,
            ["amount"] = amount,
            ["currency"] = "GNF",
            ["paymentMethod"] = paymentMethod,
            ["paymentDate"] = DateTime.UtcNow.ToString("dd/MM/yyyy HH:mm"),
            ["receiptUrl"] = $"https://nafaplace.com/orders/{orderId}/receipt"
        };

        await SendTemplatedAsync(email, "payment_confirmation", templateData);
    }

    public async Task SendPasswordResetAsync(string email, string resetLink, DateTime expiresAt)
    {
        var templateData = new Dictionary<string, object>
        {
            ["resetLink"] = resetLink,
            ["expiresAt"] = expiresAt.ToString("dd/MM/yyyy HH:mm"),
            ["supportEmail"] = "<EMAIL>"
        };

        await SendTemplatedAsync(email, "password_reset", templateData);
    }

    public async Task SendEmailVerificationAsync(string email, string verificationLink)
    {
        var templateData = new Dictionary<string, object>
        {
            ["verificationLink"] = verificationLink,
            ["supportEmail"] = "<EMAIL>"
        };

        await SendTemplatedAsync(email, "email_verification", templateData);
    }

    public async Task SendInvoiceAsync(string email, int orderId, byte[] invoicePdf)
    {
        var emailDto = new EmailNotificationDto
        {
            To = email,
            Subject = $"Facture pour la commande #{orderId}",
            HtmlBody = $"<p>Veuillez trouver ci-joint la facture pour votre commande #{orderId}.</p>",
            Attachments = new List<EmailAttachmentDto>
            {
                new()
                {
                    FileName = $"facture_{orderId}.pdf",
                    ContentType = "application/pdf",
                    Content = invoicePdf
                }
            }
        };

        await SendAsync(emailDto);
    }

    public async Task SendPromotionAsync(string email, string title, string content, string? actionUrl = null)
    {
        var templateData = new Dictionary<string, object>
        {
            ["title"] = title,
            ["content"] = content,
            ["actionUrl"] = actionUrl ?? "https://nafaplace.com/promotions",
            ["unsubscribeUrl"] = $"https://nafaplace.com/unsubscribe?email={email}"
        };

        await SendTemplatedAsync(email, "promotion", templateData);
    }

    public async Task SendNewsletterAsync(List<string> emails, string subject, string content)
    {
        var emailTasks = emails.Select(async email =>
        {
            var templateData = new Dictionary<string, object>
            {
                ["content"] = content,
                ["unsubscribeUrl"] = $"https://nafaplace.com/unsubscribe?email={email}"
            };

            var emailDto = new EmailNotificationDto
            {
                To = email,
                Subject = subject,
                HtmlBody = await RenderStringTemplateAsync(content, templateData)
            };

            return await SendAsync(emailDto);
        });

        await Task.WhenAll(emailTasks);
    }

    public async Task SendStockAlertAsync(string email, string productName, int currentStock)
    {
        var templateData = new Dictionary<string, object>
        {
            ["productName"] = productName,
            ["currentStock"] = currentStock,
            ["inventoryUrl"] = "https://nafaplace.com/seller/inventory"
        };

        await SendTemplatedAsync(email, "stock_alert", templateData);
    }

    public async Task SendReviewRequestAsync(string email, int orderId, List<OrderItemDto> items)
    {
        var templateData = new Dictionary<string, object>
        {
            ["orderId"] = orderId,
            ["items"] = items,
            ["reviewUrl"] = $"https://nafaplace.com/orders/{orderId}/review"
        };

        await SendTemplatedAsync(email, "review_request", templateData);
    }

    public async Task SendAbandonedCartAsync(string email, List<CartItemDto> cartItems, string checkoutUrl)
    {
        var templateData = new Dictionary<string, object>
        {
            ["cartItems"] = cartItems,
            ["checkoutUrl"] = checkoutUrl,
            ["totalItems"] = cartItems.Count,
            ["totalAmount"] = cartItems.Sum(item => item.Price * item.Quantity)
        };

        await SendTemplatedAsync(email, "abandoned_cart", templateData);
    }

    // Gestion des templates
    public async Task<int> CreateTemplateAsync(EmailTemplateDto template)
    {
        try
        {
            template.CreatedAt = DateTime.UtcNow;
            template.UpdatedAt = DateTime.UtcNow;
            
            // Simuler la sauvegarde en base de données
            _logger.LogInformation("Template email créé: {TemplateName}", template.Name);
            return new Random().Next(1, 1000);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du template email");
            return 0;
        }
    }

    public async Task<EmailTemplateDto?> GetTemplateAsync(string templateId)
    {
        try
        {
            // Simuler la récupération depuis la base de données
            await Task.Delay(10);
            
            return templateId switch
            {
                "welcome" => new EmailTemplateDto
                {
                    Id = "welcome",
                    Name = "Email de bienvenue",
                    Subject = "Bienvenue sur NafaPlace, {{userName}} !",
                    HtmlContent = GetWelcomeTemplate(),
                    Variables = new List<string> { "userName", "activationLink", "supportEmail" }
                },
                "order_confirmation" => new EmailTemplateDto
                {
                    Id = "order_confirmation",
                    Name = "Confirmation de commande",
                    Subject = "Confirmation de votre commande #{{orderId}}",
                    HtmlContent = GetOrderConfirmationTemplate(),
                    Variables = new List<string> { "orderId", "amount", "currency", "items", "orderDate", "trackingUrl" }
                },
                "password_reset" => new EmailTemplateDto
                {
                    Id = "password_reset",
                    Name = "Réinitialisation de mot de passe",
                    Subject = "Réinitialisation de votre mot de passe",
                    HtmlContent = GetPasswordResetTemplate(),
                    Variables = new List<string> { "resetLink", "expiresAt", "supportEmail" }
                },
                _ => null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du template {TemplateId}", templateId);
            return null;
        }
    }

    public async Task<List<EmailTemplateDto>> GetTemplatesAsync(string? category = null)
    {
        await Task.Delay(10);
        
        var templates = new List<EmailTemplateDto>
        {
            new() { Id = "welcome", Name = "Email de bienvenue", Category = "Authentification" },
            new() { Id = "order_confirmation", Name = "Confirmation de commande", Category = "Commandes" },
            new() { Id = "password_reset", Name = "Réinitialisation mot de passe", Category = "Authentification" },
            new() { Id = "promotion", Name = "Email promotionnel", Category = "Marketing" },
            new() { Id = "newsletter", Name = "Newsletter", Category = "Marketing" }
        };

        return string.IsNullOrEmpty(category) 
            ? templates 
            : templates.Where(t => t.Category == category).ToList();
    }

    public async Task<bool> UpdateTemplateAsync(EmailTemplateDto template)
    {
        try
        {
            template.UpdatedAt = DateTime.UtcNow;
            _logger.LogInformation("Template email mis à jour: {TemplateId}", template.Id);
            await Task.Delay(10);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du template {TemplateId}", template.Id);
            return false;
        }
    }

    public async Task<bool> DeleteTemplateAsync(string templateId)
    {
        try
        {
            _logger.LogInformation("Template email supprimé: {TemplateId}", templateId);
            await Task.Delay(10);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du template {TemplateId}", templateId);
            return false;
        }
    }

    public async Task<string> RenderTemplateAsync(string templateId, Dictionary<string, object> data)
    {
        try
        {
            var template = await GetTemplateAsync(templateId);
            if (template == null)
            {
                throw new ArgumentException($"Template non trouvé: {templateId}");
            }

            return await RenderStringTemplateAsync(template.HtmlContent, data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du rendu du template {TemplateId}", templateId);
            throw;
        }
    }

    public async Task<bool> TestTemplateAsync(string templateId, Dictionary<string, object> testData, string testEmail)
    {
        try
        {
            var renderedContent = await RenderTemplateAsync(templateId, testData);
            
            var testEmailDto = new EmailNotificationDto
            {
                To = testEmail,
                Subject = $"[TEST] Template {templateId}",
                HtmlBody = renderedContent
            };

            return await SendAsync(testEmailDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du test du template {TemplateId}", templateId);
            return false;
        }
    }

    // Méthodes d'aide privées
    private async Task<string> RenderStringTemplateAsync(string template, Dictionary<string, object> data)
    {
        await Task.Delay(1); // Simuler le traitement
        
        var result = template;
        
        foreach (var kvp in data)
        {
            var placeholder = $"{{{{{kvp.Key}}}}}";
            var value = kvp.Value?.ToString() ?? "";
            result = result.Replace(placeholder, value);
        }

        return result;
    }

    private string GetWelcomeTemplate()
    {
        return @"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Bienvenue sur NafaPlace</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa;'>
    <div style='max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);'>
        <!-- Header avec logo -->
        <div style='background-color: #003366; padding: 30px 20px; text-align: center;'>
            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 300' style='width: 200px; height: auto;'>
                <rect width='500' height='300' fill='#003366'/>
                <defs>
                    <linearGradient id='mainGradient' x1='0%' y1='0%' x2='100%' y2='100%'>
                        <stop offset='0%' style='stop-color:#E73C30;stop-opacity:1' />
                        <stop offset='100%' style='stop-color:#F96302;stop-opacity:1' />
                    </linearGradient>
                </defs>
                <rect x='120' y='70' width='260' height='140' rx='70' ry='70' fill='url(#mainGradient)'/>
                <path d='M175,95 L195,95 L195,185 L225,95 L245,95 L245,185 L225,185 L225,125 L195,215 L175,215 Z' fill='#ffffff'/>
                <circle cx='300' cy='140' r='25' fill='#ffffff' opacity='0.8'/>
                <path d='M300,125 L300,155 M285,140 L315,140' stroke='#F96302' stroke-width='2.5'/>
                <g transform='translate(145, 240)'>
                    <text font-family='Arial, sans-serif' font-weight='700' font-size='32' letter-spacing='1' fill='#ffffff'>
                        NAFAPLACE
                    </text>
                </g>
            </svg>
        </div>

        <!-- Contenu principal -->
        <div style='padding: 40px 30px;'>
            <h2 style='color: #003366; margin-bottom: 20px; font-size: 28px; font-weight: 600;'>Bienvenue {{userName}} !</h2>

            <p style='margin-bottom: 20px; font-size: 16px;'>
                Nous sommes ravis de vous accueillir sur <strong style='color: #E73C30;'>NafaPlace</strong>,
                la marketplace de référence en Guinée.
            </p>

            <p style='margin-bottom: 25px; font-size: 16px; line-height: 1.6;'>
                Pour activer votre compte et commencer vos achats, veuillez cliquer sur le lien ci-dessous :
            </p>

            <div style='text-align: center; margin: 35px 0;'>
                <a href='{{activationLink}}'
                   style='background: linear-gradient(135deg, #E73C30 0%, #F96302 100%);
                          color: white; padding: 15px 35px; text-decoration: none;
                          border-radius: 8px; font-weight: 600; display: inline-block;
                          font-size: 16px; box-shadow: 0 4px 12px rgba(231, 60, 48, 0.3);'>
                    ✨ Activer mon compte
                </a>
            </div>

            <p style='margin-bottom: 15px; font-size: 14px; color: #666;'>
                Si vous avez des questions, n'hésitez pas à nous contacter à {{supportEmail}}.
            </p>
        </div>

        <!-- Footer -->
        <div style='background-color: #f8f9fa; padding: 25px 30px; text-align: center; border-top: 1px solid #dee2e6;'>
            <p style='color: #6c757d; font-size: 13px; margin: 0 0 10px 0;'>
                Cordialement, l'équipe <strong style='color: #E73C30;'>NafaPlace</strong>
            </p>
            <p style='color: #6c757d; font-size: 13px; margin: 0;'>
                © 2025 <strong style='color: #E73C30;'>NafaPlace</strong> - Tous droits réservés
            </p>
            <p style='color: #6c757d; font-size: 12px; margin: 10px 0 0 0;'>
                La marketplace de référence en Guinée
            </p>
        </div>
    </div>
</body>
</html>";
    }

    private string GetOrderConfirmationTemplate()
    {
        return @"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Confirmation de commande - NafaPlace</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa;'>
    <div style='max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);'>
        <!-- Header avec logo -->
        <div style='background-color: #003366; padding: 30px 20px; text-align: center;'>
            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 300' style='width: 200px; height: auto;'>
                <rect width='500' height='300' fill='#003366'/>
                <defs>
                    <linearGradient id='mainGradient' x1='0%' y1='0%' x2='100%' y2='100%'>
                        <stop offset='0%' style='stop-color:#E73C30;stop-opacity:1' />
                        <stop offset='100%' style='stop-color:#F96302;stop-opacity:1' />
                    </linearGradient>
                </defs>
                <rect x='120' y='70' width='260' height='140' rx='70' ry='70' fill='url(#mainGradient)'/>
                <path d='M175,95 L195,95 L195,185 L225,95 L245,95 L245,185 L225,185 L225,125 L195,215 L175,215 Z' fill='#ffffff'/>
                <circle cx='300' cy='140' r='25' fill='#ffffff' opacity='0.8'/>
                <path d='M300,125 L300,155 M285,140 L315,140' stroke='#F96302' stroke-width='2.5'/>
                <g transform='translate(145, 240)'>
                    <text font-family='Arial, sans-serif' font-weight='700' font-size='32' letter-spacing='1' fill='#ffffff'>
                        NAFAPLACE
                    </text>
                </g>
            </svg>
        </div>

        <!-- Contenu principal -->
        <div style='padding: 40px 30px;'>
            <h2 style='color: #003366; margin-bottom: 20px; font-size: 28px; font-weight: 600;'>Commande confirmée #{{orderId}}</h2>

            <p style='margin-bottom: 20px; font-size: 16px;'>
                Votre commande a été confirmée le {{orderDate}}.
            </p>

            <div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                        border-left: 4px solid #28a745; border-radius: 8px; padding: 20px; margin: 25px 0;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);'>
                <p style='margin: 0; color: #155724; font-weight: bold; font-size: 18px;'>
                    💰 Montant total : {{amount}} {{currency}}
                </p>
            </div>

            <div style='text-align: center; margin: 35px 0;'>
                <a href='{{trackingUrl}}'
                   style='background: linear-gradient(135deg, #E73C30 0%, #F96302 100%);
                          color: white; padding: 15px 35px; text-decoration: none;
                          border-radius: 8px; font-weight: 600; display: inline-block;
                          font-size: 16px; box-shadow: 0 4px 12px rgba(231, 60, 48, 0.3);'>
                    📦 Suivre ma commande
                </a>
            </div>

            <p style='margin-bottom: 15px; font-size: 16px; color: #28a745; font-weight: 600;'>
                Merci pour votre confiance !
            </p>
        </div>

        <!-- Footer -->
        <div style='background-color: #f8f9fa; padding: 25px 30px; text-align: center; border-top: 1px solid #dee2e6;'>
            <p style='color: #6c757d; font-size: 13px; margin: 0 0 10px 0;'>
                Cordialement, l'équipe <strong style='color: #E73C30;'>NafaPlace</strong>
            </p>
            <p style='color: #6c757d; font-size: 13px; margin: 0;'>
                © 2025 <strong style='color: #E73C30;'>NafaPlace</strong> - Tous droits réservés
            </p>
            <p style='color: #6c757d; font-size: 12px; margin: 10px 0 0 0;'>
                La marketplace de référence en Guinée
            </p>
        </div>
    </div>
</body>
</html>";
    }

    private string GetPasswordResetTemplate()
    {
        return @"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Réinitialisation de mot de passe - NafaPlace</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa;'>
    <div style='max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);'>
        <!-- Header avec logo -->
        <div style='background-color: #003366; padding: 30px 20px; text-align: center;'>
            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 300' style='width: 200px; height: auto;'>
                <rect width='500' height='300' fill='#003366'/>
                <defs>
                    <linearGradient id='mainGradient' x1='0%' y1='0%' x2='100%' y2='100%'>
                        <stop offset='0%' style='stop-color:#E73C30;stop-opacity:1' />
                        <stop offset='100%' style='stop-color:#F96302;stop-opacity:1' />
                    </linearGradient>
                </defs>
                <rect x='120' y='70' width='260' height='140' rx='70' ry='70' fill='url(#mainGradient)'/>
                <path d='M175,95 L195,95 L195,185 L225,95 L245,95 L245,185 L225,185 L225,125 L195,215 L175,215 Z' fill='#ffffff'/>
                <circle cx='300' cy='140' r='25' fill='#ffffff' opacity='0.8'/>
                <path d='M300,125 L300,155 M285,140 L315,140' stroke='#F96302' stroke-width='2.5'/>
                <g transform='translate(145, 240)'>
                    <text font-family='Arial, sans-serif' font-weight='700' font-size='32' letter-spacing='1' fill='#ffffff'>
                        NAFAPLACE
                    </text>
                </g>
            </svg>
        </div>

        <!-- Contenu principal -->
        <div style='padding: 40px 30px;'>
            <h2 style='color: #003366; margin-bottom: 20px; font-size: 28px; font-weight: 600;'>Réinitialisation de mot de passe</h2>

            <p style='margin-bottom: 20px; font-size: 16px;'>Bonjour,</p>

            <p style='margin-bottom: 25px; font-size: 16px; line-height: 1.6;'>
                Vous avez demandé la réinitialisation de votre mot de passe. Cliquez sur le lien ci-dessous pour créer
                un nouveau mot de passe :
            </p>

            <div style='text-align: center; margin: 35px 0;'>
                <a href='{{resetLink}}'
                   style='background: linear-gradient(135deg, #E73C30 0%, #F96302 100%);
                          color: white; padding: 15px 35px; text-decoration: none;
                          border-radius: 8px; font-weight: 600; display: inline-block;
                          font-size: 16px; box-shadow: 0 4px 12px rgba(231, 60, 48, 0.3);'>
                    🔑 Réinitialiser mon mot de passe
                </a>
            </div>

            <div style='background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                        border-left: 4px solid #F96302; border-radius: 8px; padding: 20px; margin: 25px 0;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);'>
                <p style='margin: 0 0 10px 0; color: #856404; font-weight: bold; font-size: 16px;'>⚠️ Important :</p>
                <ul style='margin: 10px 0 0 0; color: #856404; font-size: 14px; line-height: 1.5;'>
                    <li>Ce lien expire le {{expiresAt}}</li>
                    <li>Si vous n'avez pas demandé cette réinitialisation, ignorez cet email</li>
                    <li>Ne partagez jamais ce lien avec personne</li>
                </ul>
            </div>

            <p style='margin-bottom: 15px; font-size: 14px; color: #666;'>Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
            <p style='word-break: break-all; color: #E73C30; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 4px; border: 1px solid #dee2e6;'>{{resetLink}}</p>
        </div>

        <!-- Footer -->
        <div style='background-color: #f8f9fa; padding: 25px 30px; text-align: center; border-top: 1px solid #dee2e6;'>
            <p style='color: #6c757d; font-size: 13px; margin: 0 0 10px 0;'>
                Cet email a été envoyé automatiquement, merci de ne pas y répondre.
            </p>
            <p style='color: #6c757d; font-size: 13px; margin: 0;'>
                © 2025 <strong style='color: #E73C30;'>NafaPlace</strong> - Tous droits réservés
            </p>
            <p style='color: #6c757d; font-size: 12px; margin: 10px 0 0 0;'>
                La marketplace de référence en Guinée | Support : {{supportEmail}}
            </p>
        </div>
    </div>
</body>
</html>";
    }

    // Méthodes non implémentées - à compléter selon les besoins
    public Task SendAccountSuspensionAsync(string email, string reason, DateTime? suspensionEnd = null) => throw new NotImplementedException();
    public Task SendAccountReactivationAsync(string email) => throw new NotImplementedException();
    public Task SendSecurityAlertAsync(string email, string alertType, string details, DateTime timestamp) => throw new NotImplementedException();
    public Task SendDataExportReadyAsync(string email, string downloadUrl, DateTime expiresAt) => throw new NotImplementedException();
    public Task SendSubscriptionExpiryAsync(string email, string planName, DateTime expiryDate) => throw new NotImplementedException();
    public Task<bool> AddToMailingListAsync(string email, string listName, Dictionary<string, object>? metadata = null) => throw new NotImplementedException();
    public Task<bool> RemoveFromMailingListAsync(string email, string listName) => throw new NotImplementedException();
    public Task<List<string>> GetMailingListsAsync(string email) => throw new NotImplementedException();
    public Task<int> SendToMailingListAsync(string listName, string subject, string content, string? templateId = null) => throw new NotImplementedException();
    public Task<int> SendToSegmentAsync(string segmentName, string subject, string content, string? templateId = null) => throw new NotImplementedException();
    public Task<bool> ScheduleEmailAsync(EmailNotificationDto email, DateTime scheduledAt) => throw new NotImplementedException();
    public Task<bool> ScheduleBulkEmailAsync(List<EmailNotificationDto> emails, DateTime scheduledAt) => throw new NotImplementedException();
    public Task<bool> CancelScheduledEmailAsync(int scheduledEmailId) => throw new NotImplementedException();
    public Task ProcessScheduledEmailsAsync() => throw new NotImplementedException();
    public Task<List<ScheduledEmailDto>> GetScheduledEmailsAsync(DateTime? scheduledBefore = null) => throw new NotImplementedException();
    public Task<int> CreateCampaignAsync(EmailCampaignDto campaign) => throw new NotImplementedException();
    public Task<EmailCampaignDto?> GetCampaignAsync(int campaignId) => throw new NotImplementedException();
    public Task<List<EmailCampaignDto>> GetCampaignsAsync(string? status = null) => throw new NotImplementedException();
    public Task<bool> StartCampaignAsync(int campaignId) => throw new NotImplementedException();
    public Task<bool> PauseCampaignAsync(int campaignId) => throw new NotImplementedException();
    public Task<bool> StopCampaignAsync(int campaignId) => throw new NotImplementedException();
    public Task<EmailCampaignStatsDto> GetCampaignStatsAsync(int campaignId) => throw new NotImplementedException();
    public Task<EmailStatsDto> GetEmailStatsAsync(DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<List<EmailDeliveryDto>> GetDeliveryHistoryAsync(string? email = null, int limit = 50) => throw new NotImplementedException();
    public Task<double> GetDeliveryRateAsync(DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<double> GetOpenRateAsync(DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<double> GetClickRateAsync(DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<Dictionary<string, int>> GetEmailStatsByTemplateAsync() => throw new NotImplementedException();
    public Task TrackEmailOpenAsync(string trackingId) => throw new NotImplementedException();
    public Task TrackEmailClickAsync(string trackingId, string url) => throw new NotImplementedException();
    public Task TrackEmailBounceAsync(string trackingId, string reason) => throw new NotImplementedException();
    public Task TrackEmailUnsubscribeAsync(string trackingId, string email) => throw new NotImplementedException();
    public Task<List<string>> GetBouncedEmailsAsync(DateTime? since = null) => throw new NotImplementedException();
    public Task<List<string>> GetUnsubscribedEmailsAsync(DateTime? since = null) => throw new NotImplementedException();
    public Task<bool> AddToSuppressionListAsync(string email, string reason) => throw new NotImplementedException();
    public Task<bool> RemoveFromSuppressionListAsync(string email) => throw new NotImplementedException();
    public Task<bool> IsEmailSuppressedAsync(string email) => Task.FromResult(false);
    public Task<int> CleanupBouncedEmailsAsync(int daysToKeep = 30) => throw new NotImplementedException();
    public Task<bool> ValidateEmailAsync(string email) => Task.FromResult(Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$"));
    public Task<bool> ValidateTemplateAsync(string templateContent) => Task.FromResult(true);
    public Task<Dictionary<string, object>> GetEmailConfigAsync() => throw new NotImplementedException();
    public Task<bool> UpdateEmailConfigAsync(Dictionary<string, object> config) => throw new NotImplementedException();
    public Task<bool> TestEmailServiceAsync(string testEmail) => SendAsync(new EmailNotificationDto { To = testEmail, Subject = "Test", HtmlBody = "Test email" });
    public Task<Dictionary<string, bool>> GetServiceHealthAsync() => Task.FromResult(new Dictionary<string, bool> { ["smtp"] = true });
    public Task<string> PersonalizeContentAsync(string content, Dictionary<string, object> userData) => RenderStringTemplateAsync(content, userData);
    public Task<int> CreateABTestAsync(ABTestEmailDto abTest) => Task.FromResult(0);
    public Task<ABTestResultDto> GetABTestResultsAsync(int abTestId) => Task.FromResult(new ABTestResultDto { TestId = abTestId, WinningVariant = "A", ConfidenceLevel = 95.0 });
    public Task<string> GetOptimalVariantAsync(int abTestId) => Task.FromResult("A");
    public Task<byte[]> ExportEmailStatsAsync(DateTime startDate, DateTime endDate, string format = "csv") => throw new NotImplementedException();
    public Task<byte[]> ExportCampaignReportAsync(int campaignId, string format = "pdf") => throw new NotImplementedException();
    public Task<byte[]> ExportSubscriberListAsync(string listName, string format = "csv") => throw new NotImplementedException();
}
