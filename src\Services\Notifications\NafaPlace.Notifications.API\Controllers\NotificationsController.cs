using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Notifications.Application.DTOs;
using NafaPlace.Notifications.Application.Services;
using NafaPlace.Notifications.Application.Interfaces;
using System.Security.Claims;

namespace NafaPlace.Notifications.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class NotificationsController : ControllerBase
{
    private readonly INotificationService _notificationService;
    private readonly NafaPlace.Notifications.Application.Interfaces.IEmailService _emailService;

    public NotificationsController(INotificationService notificationService, NafaPlace.Notifications.Application.Interfaces.IEmailService emailService)
    {
        _notificationService = notificationService;
        _emailService = emailService;
    }

    [HttpGet]
    public async Task<ActionResult<NotificationsPagedResult>> GetNotifications(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] bool unreadOnly = false)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var result = await _notificationService.GetUserNotificationsAsync(userId, page, pageSize, unreadOnly);
        return Ok(result);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<NotificationDto>> GetNotification(int id)
    {
        var notification = await _notificationService.GetNotificationByIdAsync(id);
        if (notification == null)
            return NotFound();

        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (notification.UserId != userId)
            return Forbid();

        return Ok(notification);
    }

    [HttpGet("recent")]
    public async Task<ActionResult<List<NotificationDto>>> GetRecentNotifications([FromQuery] int count = 5)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var notifications = await _notificationService.GetRecentNotificationsAsync(userId, count);
        return Ok(notifications);
    }

    [HttpGet("user/{userId}/recent")]
    [AllowAnonymous] // Pour les tests, en production utiliser [Authorize]
    public async Task<ActionResult<List<NotificationDto>>> GetUserRecentNotifications(string userId, [FromQuery] int count = 5)
    {
        var notifications = await _notificationService.GetRecentNotificationsAsync(userId, count);
        return Ok(notifications);
    }

    [HttpGet("stats")]
    public async Task<ActionResult<NotificationStatsDto>> GetStats()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var stats = await _notificationService.GetUserStatsAsync(userId);
        return Ok(stats);
    }

    [HttpPost]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<NotificationDto>> CreateNotification([FromBody] CreateNotificationRequest request)
    {
        try
        {
            var notification = await _notificationService.CreateNotificationAsync(request);
            return CreatedAtAction(nameof(GetNotification), new { id = notification.Id }, notification);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPost("bulk")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<List<NotificationDto>>> CreateBulkNotification([FromBody] BulkNotificationRequest request)
    {
        try
        {
            var notifications = await _notificationService.CreateBulkNotificationAsync(request);
            return Ok(notifications);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPut("{id}/read")]
    public async Task<IActionResult> MarkAsRead(int id)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var result = await _notificationService.MarkAsReadAsync(id, userId);
        if (!result)
            return NotFound();

        return Ok();
    }

    [HttpPut("mark-all-read")]
    public async Task<ActionResult<int>> MarkAllAsRead()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var count = await _notificationService.MarkAllAsReadAsync(userId);
        return Ok(new { markedCount = count });
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteNotification(int id)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var result = await _notificationService.DeleteNotificationAsync(id, userId);
        if (!result)
            return NotFound();

        return NoContent();
    }

    [HttpDelete("read")]
    public async Task<ActionResult<int>> DeleteAllRead()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var count = await _notificationService.DeleteAllReadAsync(userId);
        return Ok(new { deletedCount = count });
    }

    // Template endpoints
    [HttpGet("templates")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<List<NotificationTemplateDto>>> GetTemplates()
    {
        var templates = await _notificationService.GetTemplatesAsync();
        return Ok(templates);
    }

    [HttpPost("templates")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<NotificationTemplateDto>> CreateTemplate([FromBody] CreateNotificationTemplateRequest request)
    {
        try
        {
            var template = await _notificationService.CreateTemplateAsync(request);
            return Ok(template);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPost("from-template/{templateId}")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<NotificationDto>> CreateFromTemplate(
        int templateId,
        [FromBody] Dictionary<string, string> parameters,
        [FromQuery] string userId)
    {
        try
        {
            var notification = await _notificationService.CreateFromTemplateAsync(templateId, userId, parameters);
            return Ok(notification);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    // Email endpoints
    [HttpPost("email/password-changed")]
    [AllowAnonymous] // Pour permettre aux autres services d'appeler
    public async Task<IActionResult> SendPasswordChangedEmail([FromBody] PasswordChangedEmailRequest request)
    {
        try
        {
            var subject = "Votre mot de passe a été modifié - NafaPlace";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <div style='text-align: center; margin-bottom: 30px;'>
                            <h1 style='color: #E73C30;'>NafaPlace</h1>
                        </div>

                        <h2 style='color: #003366;'>Mot de passe modifié</h2>

                        <p>Bonjour {request.UserName},</p>

                        <p>Nous vous confirmons que votre mot de passe a été modifié avec succès le <strong>{DateTime.Now:dd/MM/yyyy à HH:mm}</strong>.</p>

                        <div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                            <p style='margin: 0;'><strong>⚠️ Si vous n'êtes pas à l'origine de cette modification :</strong></p>
                            <ul style='margin: 10px 0;'>
                                <li>Connectez-vous immédiatement à votre compte</li>
                                <li>Changez votre mot de passe</li>
                                <li>Contactez notre support si nécessaire</li>
                            </ul>
                        </div>

                        <p>Pour votre sécurité, nous vous recommandons de :</p>
                        <ul>
                            <li>Utiliser un mot de passe unique et complexe</li>
                            <li>Ne jamais partager vos identifiants</li>
                            <li>Vous déconnecter après chaque session</li>
                        </ul>

                        <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>
                            <p style='color: #666; font-size: 14px;'>
                                Cet email a été envoyé automatiquement, merci de ne pas y répondre.<br>
                                © 2025 NafaPlace - Tous droits réservés
                            </p>
                        </div>
                    </div>
                </body>
                </html>";

            try
            {
                await _emailService.SendEmailAsync(request.Email, subject, body, true);
                return Ok(new { message = "Email de confirmation envoyé avec succès" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Erreur lors de l'envoi de l'email");
            }
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPost("email/password-reset")]
    [AllowAnonymous]
    public async Task<IActionResult> SendPasswordResetEmail([FromBody] PasswordResetEmailRequest request)
    {
        try
        {
            var subject = "Réinitialisation de votre mot de passe - NafaPlace";
            var body = $@"
                <html>
                <head>
                    <meta charset='utf-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>Réinitialisation de mot de passe - NafaPlace</title>
                </head>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa;'>
                    <div style='max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);'>
                        <!-- Header avec logo -->
                        <div style='background-color: #003366; padding: 30px 20px; text-align: center;'>
                            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 300' style='width: 200px; height: auto;'>
                                <rect width='500' height='300' fill='#003366'/>
                                <defs>
                                    <linearGradient id='mainGradient' x1='0%' y1='0%' x2='100%' y2='100%'>
                                        <stop offset='0%' style='stop-color:#E73C30;stop-opacity:1' />
                                        <stop offset='100%' style='stop-color:#F96302;stop-opacity:1' />
                                    </linearGradient>
                                </defs>
                                <rect x='120' y='70' width='260' height='140' rx='70' ry='70' fill='url(#mainGradient)'/>
                                <path d='M175,95 L195,95 L195,185 L225,95 L245,95 L245,185 L225,185 L225,125 L195,215 L175,215 Z' fill='#ffffff'/>
                                <circle cx='300' cy='140' r='25' fill='#ffffff' opacity='0.8'/>
                                <path d='M300,125 L300,155 M285,140 L315,140' stroke='#F96302' stroke-width='2.5'/>
                                <g transform='translate(145, 240)'>
                                    <text font-family='Arial, sans-serif' font-weight='700' font-size='32' letter-spacing='1' fill='#ffffff'>
                                        NAFAPLACE
                                    </text>
                                </g>
                            </svg>
                        </div>

                        <!-- Contenu principal -->
                        <div style='padding: 40px 30px;'>

                            <h2 style='color: #003366; margin-bottom: 20px; font-size: 28px; font-weight: 600;'>Réinitialisation de mot de passe</h2>

                            <p style='margin-bottom: 20px; font-size: 16px;'>Bonjour {request.UserName},</p>

                            <p style='margin-bottom: 25px; font-size: 16px; line-height: 1.6;'>
                                Vous avez demandé la réinitialisation de votre mot de passe. Cliquez sur le lien ci-dessous pour créer
                                un nouveau mot de passe :
                            </p>

                            <div style='text-align: center; margin: 35px 0;'>
                                <a href='{request.ResetLink}'
                                   style='background: linear-gradient(135deg, #E73C30 0%, #F96302 100%);
                                          color: white; padding: 15px 35px; text-decoration: none;
                                          border-radius: 8px; font-weight: 600; display: inline-block;
                                          font-size: 16px; box-shadow: 0 4px 12px rgba(231, 60, 48, 0.3);'>
                                    🔑 Réinitialiser mon mot de passe
                                </a>
                            </div>

                            <div style='background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                                        border-left: 4px solid #F96302; border-radius: 8px; padding: 20px; margin: 25px 0;
                                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);'>
                                <p style='margin: 0 0 10px 0; color: #856404; font-weight: bold; font-size: 16px;'>⚠️ Important :</p>
                                <ul style='margin: 10px 0 0 0; color: #856404; font-size: 14px; line-height: 1.5;'>
                                    <li>Ce lien expire dans 24 heures</li>
                                    <li>Si vous n'avez pas demandé cette réinitialisation, ignorez cet email</li>
                                    <li>Ne partagez jamais ce lien avec personne</li>
                                </ul>
                            </div>

                            <p style='margin-bottom: 15px; font-size: 14px; color: #666;'>Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :</p>
                            <p style='word-break: break-all; color: #E73C30; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 4px; border: 1px solid #dee2e6;'>{request.ResetLink}</p>
                        </div>

                        <!-- Footer -->
                        <div style='background-color: #f8f9fa; padding: 25px 30px; text-align: center; border-top: 1px solid #dee2e6;'>
                            <p style='color: #6c757d; font-size: 13px; margin: 0 0 10px 0;'>
                                Cet email a été envoyé automatiquement, merci de ne pas y répondre.
                            </p>
                            <p style='color: #6c757d; font-size: 13px; margin: 0;'>
                                © 2025 <strong style='color: #E73C30;'>NafaPlace</strong> - Tous droits réservés
                            </p>
                            <p style='color: #6c757d; font-size: 12px; margin: 10px 0 0 0;'>
                                La marketplace de référence en Guinée
                            </p>
                        </div>
                    </div>
                </body>
                </html>";

            try
            {
                await _emailService.SendEmailAsync(request.Email, subject, body, true);
                return Ok(new { message = "Email de réinitialisation envoyé avec succès" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Erreur lors de l'envoi de l'email");
            }
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }
}

// DTOs pour les emails
public class PasswordChangedEmailRequest
{
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
}

public class PasswordResetEmailRequest
{
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string ResetLink { get; set; } = string.Empty;
}
