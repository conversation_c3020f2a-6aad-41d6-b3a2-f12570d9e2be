@page "/forgot-password"
@using NafaPlace.SellerPortal.Models
@using NafaPlace.SellerPortal.Services
@inject AuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>Mot de passe oublié - Seller Portal</PageTitle>

<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100" style="background-color: #f8f9fa;">
    <div class="col-md-4 col-lg-3">
        <div class="card border-0 shadow-sm" style="border-radius: 12px;">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="fas fa-key text-primary" style="font-size: 2.5rem;"></i>
                    </div>
                    <h4 class="fw-bold text-dark mb-1">Mot de passe oublié</h4>
                    <p class="text-muted small mb-0">Entrez votre email pour recevoir un lien de réinitialisation</p>
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>@errorMessage
                    </div>
                }

                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>@successMessage
                    </div>
                }

                @if (!emailSent)
                {
                    <EditForm Model="@forgotPasswordRequest" OnValidSubmit="HandleForgotPasswordSubmit">
                        <DataAnnotationsValidator />

                        <div class="mb-3">
                            <label for="email" class="form-label">Adresse email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <InputText type="email" class="form-control" id="email" placeholder="<EMAIL>" @bind-Value="forgotPasswordRequest.Email" />
                            </div>
                            <ValidationMessage For="@(() => forgotPasswordRequest.Email)" />
                        </div>

                        <div class="d-grid gap-2 mb-3">
                            <button type="submit" class="btn btn-primary py-2" disabled="@isLoading" style="border-radius: 8px; font-weight: 500;">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-2">Envoi en cours...</span>
                                }
                                else
                                {
                                    <span><i class="fas fa-paper-plane me-2"></i>Envoyer le lien</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                }

                <div class="text-center">
                    <p class="small text-muted mb-2">Vous vous souvenez de votre mot de passe ?</p>
                    <a href="/login" class="text-primary text-decoration-none small fw-bold">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la connexion
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private ForgotPasswordRequest forgotPasswordRequest = new();
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;
    private bool emailSent = false;

    private async Task HandleForgotPasswordSubmit()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            // Appeler l'API pour initier la réinitialisation
            var response = await AuthService.ForgotPasswordAsync(forgotPasswordRequest);

            if (response.Success)
            {
                emailSent = true;
                successMessage = "Si cette adresse email existe, un lien de réinitialisation a été envoyé. Vérifiez votre boîte de réception.";
            }
            else
            {
                errorMessage = response.Message ?? "Une erreur s'est produite. Veuillez réessayer.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
